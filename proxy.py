import requests
from urllib.parse import urlparse

def test_https_proxy(proxy_url, test_url="https://www.youtube.com", timeout=10):
    """
    验证HTTPS代理是否可用
    
    参数:
        proxy_url (str): 代理地址，格式如 '*********************:port' 或 'http://host:port'
        test_url (str): 用于测试的URL，默认为https://www.example.com
        timeout (int): 超时时间(秒)
    
    返回:
        tuple: (是否成功, 响应时间毫秒或错误消息)
    """
    proxies = {
        'http': proxy_url,
        'https': proxy_url
    }
    
    try:
        # 解析代理URL以验证格式
        parsed = urlparse(proxy_url)
        if not all([parsed.scheme in ('http', 'https'), parsed.netloc]):
            return False, "Invalid proxy URL format"
        
        # 记录开始时间
        start_time = time.time()
        
        # 发送请求
        response = requests.get(
            test_url,
            proxies=proxies,
            timeout=timeout,
            headers={'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3'}
        )
        
        # 计算响应时间
        response_time = int((time.time() - start_time) * 1000)
        
        # 检查响应状态码
        if response.status_code == 200:
            return True, f"Success (Response time: {response_time}ms)"
        else:
            return False, f"Failed with status code: {response.status_code}"
            
    except requests.exceptions.ProxyError as e:
        return False, f"Proxy Error: {str(e)}"
    except requests.exceptions.ConnectTimeout:
        return False, "Connection Timeout"
    except requests.exceptions.SSLError:
        return False, "SSL Error (Possible MITM interception)"
    except requests.exceptions.RequestException as e:
        return False, f"Request Error: {str(e)}"
    except Exception as e:
        return False, f"Unexpected Error: {str(e)}"

if __name__ == "__main__":
    import time
    
    # 示例用法
    proxy_to_test = "http://proxy.proxy302.com:2222:P6jBGcCR:oBMGl87dQmU5KDYm"  # 替换为你的代理
    # 或者不需要认证的代理: "http://proxy.example.com:8080"
    
    print(f"Testing proxy: {proxy_to_test}")
    success, message = test_https_proxy(proxy_to_test)
    
    if success:
        print(f"✅ Proxy is working: {message}")
    else:
        print(f"❌ Proxy is not working: {message}")